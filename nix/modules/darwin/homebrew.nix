_: {
  # 保留部分 GUI 应用使用 Homebrew
  homebrew = {
    enable = true;
    taps = [
      "kilvn/homebrew-schedule"
    ];
    brews = [
      # "cloudflare-wrangler"
      # 安全工具 (可选)
      # "clamav"          # 防病毒软件
      # "lynis"           # 安全审计工具
      # "nmap"            # 网络扫描工具
      # "fail2ban"        # 入侵防护 (需要额外配置)
    ];
    casks = [
      "alfred"
      "kilvn/homebrew-schedule/clashx-meta"
      "goland"
      "google-chrome"
      "hammerspoon"
      "orbstack"
      "reqable"
      "tencent-lemon"
      "wechat"
      "wireshark-app"
    ];
    # PLAN 目前orbstack 需要sonoma才能install，所以暂时注释。换MBP之后，开启这个配置，以及autoUpdate。完全由nix管理brew
    # greedyCasks = true;

    onActivation = {
      cleanup = "zap"; # 只安装nix配置的pkg，除此之外全部移除
      autoUpdate = false; # 每次rebuild时，自动升级brew
      upgrade = false;
    };
  };
}
